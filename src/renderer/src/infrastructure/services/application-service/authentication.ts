import type { GlobalStorageService } from '@renderer/infrastructure/services'
import { useGlobalStorageService } from '@renderer/infrastructure/services'
import type { CloudAuthenticationService } from '@renderer/infrastructure/services/entity-service/cloud/cloud-authentication-service'
import { useCloudAuthenticationService } from '@renderer/infrastructure/services/entity-service/cloud/cloud-authentication-service'
import { useMemo } from 'react'

export function useLoginService() {
  const globalStorageService = useGlobalStorageService()
  const cloudAuthenticationService = useCloudAuthenticationService()
  return useMemo(
    () => new LoginService(globalStorageService, cloudAuthenticationService),
    [cloudAuthenticationService, globalStorageService],
  )
}

class LoginService {
  constructor(
    private globalStorageService: GlobalStorageService,
    private cloudAuthenticationService: CloudAuthenticationService,
  ) {}

  sendVerifyCode(phone: string): Promise<string> {
    return this.cloudAuthenticationService.sendVerifyCode(phone)
  }

  async login(params: Parameters<CloudAuthenticationService['authentic']>[0]): Promise<void> {
    const result = await this.cloudAuthenticationService.authentic(params)
    this.globalStorageService.setToken(result.authorization)
  }

  async loginV2(params: Parameters<CloudAuthenticationService['authenticV2']>[0]): Promise<void> {
    const result = await this.cloudAuthenticationService.authenticV2(params)
    this.globalStorageService.setToken(result.authorization)
  }
}
