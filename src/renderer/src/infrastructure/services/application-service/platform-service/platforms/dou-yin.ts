import { PlatformService } from '../platform-service'
import type { AccountSession, MusicPageWrapper, PlatformDataItem } from '@common/structure'
import type { DouyinLocationInfo } from '@yixiaoer/platform-service/dist/media-platform/douyin/douyin-location.service'
import { uiEvents } from '@common/events/ui-events'
import type { DouyinFriendResultOutput, DouyinTopicInfo } from '@yixiaoer/platform-service'
import type {
  DouyinMusicCategory,
  DouyinMusicInfo,
} from '@yixiaoer/platform-service/dist/media-platform/douyin/douyin'
import type { Account } from '@renderer/infrastructure/model'

class DouYinPlatformService extends PlatformService {
  async getTopics(session: AccountSession, keyword: string) {
    return this.getData('抖音话题', () =>
      window.api.invoke<PlatformDataItem<DouyinTopicInfo>[]>(
        uiEvents.platform.douYin.getTopics,
        session.cookies,
        keyword,
      ),
    )
  }

  async getFriends(session: AccountSession, keyword: string) {
    return this.getData('抖音At好友', () =>
      window.api.invoke<PlatformDataItem<DouyinFriendResultOutput>[]>(
        uiEvents.platform.douYin.getFriends,
        session.cookies,
        keyword,
      ),
    )
  }

  async getLocations(
    account: Account,
    session: AccountSession,
    keyword: string,
    locationType: number,
  ) {
    return this.getData('抖音位置', () =>
      window.api.invoke<PlatformDataItem<DouyinLocationInfo>[]>(
        uiEvents.platform.douYin.getLocations,
        session.cookies,
        keyword,
        locationType,
      ),
    )
  }

  async checkBringLocations(account: Account, session: AccountSession) {
    return this.getData('检测全国地址权限', () =>
      window.api.invoke<boolean>(uiEvents.platform.douYin.checkBringLocations, session.cookies),
    )
  }

  async getBringLocations(account: Account, session: AccountSession, keyword: string) {
    return this.getData('抖音带货位置', () =>
      window.api.invoke<PlatformDataItem<DouyinLocationInfo>[]>(
        uiEvents.platform.douYin.getLocations,
        session.cookies,
        keyword,
      ),
    )
  }

  // 获取 music 分类
  async getMusicCategories() {
    return this.getData('抖音音乐分类', () =>
      window.api.invoke<PlatformDataItem<DouyinMusicCategory>[]>(
        uiEvents.platform.douYin.getMusicCategories,
      ),
    )
  }

  // 获取 music
  async getMusic(session: AccountSession, cursor?: number, keyword?: string, categoryId?: string) {
    return this.getData('抖音音乐', () =>
      window.api.invoke<MusicPageWrapper<DouyinMusicInfo>>(
        uiEvents.platform.douYin.getMusic,
        session.cookies,
        cursor,
        keyword,
        categoryId,
      ),
    )
  }
}

export const douYinPlatformService = new DouYinPlatformService()
