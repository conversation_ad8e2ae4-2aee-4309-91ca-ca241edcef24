import type { LiteralObject } from '@common/infrastructure/type/literal-object'
import type {
  AccountSession,
  OverviewContentType,
  PlatformDataItem,
  PublishOverviewHeaderItem,
  PublishOverviewRawItem,
  QueryStateResult,
  SessionDetectionResult,
} from '@common/structure'
import {
  toAccountInfoStructure,
  type Account,
  type SpiderAccount,
} from '@renderer/infrastructure/model/account'
import type {
  PushingTask,
  PushingTaskSetIdentifier,
} from '@renderer/infrastructure/model/pushing-task'
import { uiEvents } from '@common/events/ui-events'
import { notifyService } from '@renderer/hooks/use-notify'
import type { CloudPublishTaskVideoFromLite, DataColumn } from '@yixiaoer/platform-service'
import type { EditContentType } from '@common/model/content-type'

export class PlatformService {
  async pushToPlatform(
    task: PushingTask,
    account: Account,
    wechatLockToken: [string, string] | null,
    session: AccountSession,
    body: LiteralObject,
  ) {
    try {
      console.group(
        '准备推送任务：',
        task,
        task.taskId,
        task.platform.name,
        task.accountId,
        wechatLockToken,
      )
      console.debug(body, session.cookies)
      console.groupEnd()

      await window.api.invoke<void>(
        uiEvents.pushVideo,
        task.taskId,
        task.platform.name,
        task.pushContentType,
        account.accountId,
        account.authorId,
        wechatLockToken,
        session.cookies,
        body,
      )
    } catch (error) {
      console.error('初始化推送任务失败：', task.taskId, error)
      notifyService.error('初始化推送任务失败：' + error)
    }
  }

  async schedulePlatformStateQueryNew(taskSetId: PushingTaskSetIdentifier, scheduleDatetime: Date) {
    try {
      await window.api.invoke<void>(uiEvents.scheduleStateQuery, taskSetId, scheduleDatetime)
    } catch (error) {
      console.error('预约查询任务失败：', taskSetId, error)
    }
  }

  async auditStateQuery(
    taskId: string,
    platformName: string,
    contentType: EditContentType,
    account: SpiderAccount,
    publishId: string,
  ) {
    try {
      return await window.api.invoke<QueryStateResult>(
        uiEvents.auditStateQuery,
        taskId,
        platformName,
        contentType,
        toAccountInfoStructure(account),
        publishId,
      )
    } catch (error) {
      console.error('查询任务状态失败：', taskId, error)
      throw error
    }
  }

  /**
   *
   * @param account
   * @param session
   * @param checksum
   * @param localonly 仅本地检测
   */
  sessionDetect(
    account: Account,
    session: AccountSession,
    checksum: string,
    localonly: boolean = false,
  ) {
    return window.api.invoke<SessionDetectionResult>(
      uiEvents.sessionDetect,
      account.platform.name,
      account.accountId,
      account.sessionState,
      session,
      checksum,
      localonly,
    )
  }

  protected async getData<T>(name: string, getter: () => T | PromiseLike<T>) {
    try {
      const result = await getter()
      console.debug(`获取${name}成功`, result)
      return result
    } catch (e) {
      console.error(`获取${name}失败`, e)
      throw e
    }
  }

  async getLocations(
    account: Account,
    session: AccountSession,
    keyword: string,
    locationType?: number,
  ): Promise<PlatformDataItem[]> {
    console.debug('未实现的获取位置方法', session, keyword, locationType)
    throw new Error('未实现的获取位置方法')
  }

  async queryAccountOverview(account: Account, platformName: string, session: AccountSession) {
    const result = await window.api.invoke<{
      video?: DataColumn[]
      dynamic?: DataColumn[]
      article?: DataColumn[]
    }>(uiEvents.queryAccountOverview, platformName, account.accountId, session.cookies)

    return result
  }

  async queryPublishOverviews(
    account: Account,
    platformName: string,
    accountSession: AccountSession,
  ) {
    const result = await window.api.invoke<PublishOverviewRawItem[]>(
      uiEvents.queryPublishOverviews,
      platformName,
      account.accountId,
      accountSession.cookies,
    )
    return result
  }

  queryPublishOverviewsHeaders(platformName: string, contentType: OverviewContentType | undefined) {
    return window.api.invoke<PublishOverviewHeaderItem[]>(
      uiEvents.queryPublishOverviewsHeaders,
      platformName,
      contentType,
    )
  }

  pushAppTask(body: CloudPublishTaskVideoFromLite) {
    return window.api.invoke<void>(uiEvents.pushAppTask, body)
  }

  getUnfinishedTaskIds() {
    return window.api.invoke<string[]>(uiEvents.getUnfinishedTaskIds)
  }

  cancelPublishTask(taskId: string) {
    return window.api.invoke<void>(uiEvents.cancelPublishTask, taskId)
  }
}

export const platformService = new PlatformService()
