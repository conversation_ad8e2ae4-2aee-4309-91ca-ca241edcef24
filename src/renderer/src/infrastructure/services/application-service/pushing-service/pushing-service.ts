import type { PushingTaskSetIdentifier } from '@renderer/infrastructure/model/pushing-task'
import {
  Publish,
  PushingTask,
  PushingTaskPushResult,
  PushingTaskSet,
} from '@renderer/infrastructure/model/pushing-task'
import {
  datetimeService,
  electronService,
  identifierService,
  usePersistentLogService,
} from '../infrastructure-service'

import { eventBus } from '@renderer/infrastructure/event-bus/buses'
import { pushEvents } from '@renderer/infrastructure/event-bus/business-events'
import type {
  Account,
  ArticlePublishViewModel,
  ImageTextPublishViewModel,
  MultipleVideoAccountViewModel,
  Operator,
  PublishFeatureParams,
  PushingTaskSetViewModel,
  SingleVideoAccountViewModel,
  SpiderAccount,
  VideoContentViewModel,
} from '@renderer/infrastructure/model'
import {
  usePlatformAuditResultStore,
  usePushingTaskApi,
  usePushingTaskPushResultStore,
  usePushingTaskStore,
  useSchedulingPlatformStateQueryStore,
} from '../../entity-service'

import { EditContentType, PushContentType } from '@common/model/content-type'
import type { AccountSession, QueryStateResult } from '@common/structure'
import { useUserInfo } from '@renderer/hooks/preload/use-user-info'
import {
  needQueryAuditResult,
  WechatShiPinHao3rdPartySubAccount,
} from '@renderer/infrastructure/model'
import type {
  AllPushingConfig,
  LocalPushingConfig,
} from '@renderer/infrastructure/model/config/pushing-config'
import {
  CloudVideoPushingConfig,
  LocalArticlePushingConfig,
  LocalImageTextPushingConfig,
  LocalVideoPushingConfig,
} from '@renderer/infrastructure/model/config/pushing-config'
import { features } from '@renderer/infrastructure/model/features/features'
import {
  PlatformAuditResult,
  PlatformResultStage,
  PlatformResultStageStatus,
} from '@renderer/infrastructure/model/pushing-task/platform-audit-result'
import { PushingTaskSetOperator } from '@renderer/infrastructure/model/pushing-task/pushing-task-set-operator'
import { getTaskState } from '@renderer/infrastructure/model/pushing-task/task-state'
import { useDBTransaction } from '@renderer/infrastructure/repository/transaction'
import {
  htmlService,
  platformService,
  useAuthorizeService,
  useFeatureManager,
} from '@renderer/infrastructure/services'
import { contentTypeClassifier } from '@renderer/infrastructure/services/application-service/pushing-service/content-type-classifier'
import { usePushingTaskSetApi } from '@renderer/infrastructure/services/entity-service/cloud/pushing-task-set-api'
import { usePublishStore } from '@renderer/infrastructure/services/entity-service/publish-store'
import { usePushingConfigStore } from '@renderer/infrastructure/services/entity-service/pushing-config-store'
import { usePushingTaskAccountStore } from '@renderer/infrastructure/services/entity-service/pushing-task-account-store'
import { usePushingTaskSetOperatorStore } from '@renderer/infrastructure/services/entity-service/pushing-task-set-operator-store'
import { usePushingTaskSetReportingStateStore } from '@renderer/infrastructure/services/entity-service/pushing-task-set-reporting-state-store'
import { usePushingTaskSetStore } from '@renderer/infrastructure/services/entity-service/pushing-task-set-store'
import type { ContentTypeFilterOption } from '@renderer/pages/Publish/components/ContentTypeFilter'
import type { PushSetStateFilterOption } from '@renderer/pages/Publish/components/PushStateFilter'
import { isModernPlatform } from '@renderer/pages/Publish/specification/content-type/video/video-specification'
import { useCallback, useMemo } from 'react'
import { useAssetLibraryService } from '../assetLibrary-service'
import { bodyConverter } from './body-converter'
import { useWechat3rdPartyService } from '../account/wechat-3rd-party-service'
import { BusinessError } from '@renderer/infrastructure/model/error/businessError'
import { notifyService } from '@renderer/hooks/use-notify'
import type {
  ArticleTaskSetForm,
  ImageFormItem,
  ImageTextTaskSetForm,
  VideoFormItem,
} from '@renderer/infrastructure/types'
import { type PublishAccountRequest, type VideoTaskSetForm } from '@renderer/infrastructure/types'
import { usePublishFileService } from './publish-file-service'
import { platformNames } from '@common/model/platform-name'
import { FileProtocol, fileUrl2Path, isFileUrl } from '@common/protocol'
import { useLocalFileService } from '../infrastructure-service/LocalFileService'
import axios from 'axios'
import { useInnerContextStore } from '@renderer/store/contextStore'
// import { PubContentEnum } from '@yixiaoer/platform-service/dist/cloud/model/TypeEnum'

// 处理HTML内容中的媒体文件协议转换
async function processMediaFiles(
  doc: Document,
  isCloudMode: boolean,
  publishFileService: ReturnType<typeof usePublishFileService>,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  notifyService: any, // NotifyService type - using any to avoid complex type imports
): Promise<void> {
  // 用于记录已处理的文件，避免重复上传
  const processedFiles = new Set<string>()
  const uploadedFiles = new Map<string, string>() // path -> filekey

  // 处理单个文件URL的通用函数
  const processFileUrl = async (url: string, errorType: string): Promise<string> => {
    const path = fileUrl2Path(url)

    // 如果文件已处理过，直接返回转换后的URL
    if (processedFiles.has(path)) {
      return FileProtocol + path
    }

    try {
      if (isCloudMode) {
        // 避免重复上传同一文件
        if (!uploadedFiles.has(path)) {
          const filekey = await publishFileService.uploadFile(path)
          uploadedFiles.set(path, filekey)
        }
      }

      processedFiles.add(path)
      return FileProtocol + path
    } catch (e) {
      notifyService.error(errorType)
      throw e
    }
  }

  // 处理图片
  const images = doc.querySelectorAll('img')
  for (const image of images) {
    if (isFileUrl(image.src)) {
      const newSrc = await processFileUrl(image.src, '发布图片上传失败')
      if (isCloudMode && uploadedFiles.has(fileUrl2Path(image.src))) {
        image.setAttribute('filekey', uploadedFiles.get(fileUrl2Path(image.src))!)
      }
      image.setAttribute('src', newSrc)
    }
  }

  // 处理视频div容器的data-src属性
  const videoDivs = doc.querySelectorAll('div.tiptap-video')
  for (const videoDiv of videoDivs) {
    const dataSrc = videoDiv.getAttribute('data-src')
    if (dataSrc && isFileUrl(dataSrc)) {
      const errorMsg = isCloudMode ? '发布视频上传失败' : '发布视频处理失败'
      const newDataSrc = await processFileUrl(dataSrc, errorMsg)
      if (isCloudMode && uploadedFiles.has(fileUrl2Path(dataSrc))) {
        videoDiv.setAttribute('filekey', uploadedFiles.get(fileUrl2Path(dataSrc))!)
      }
      videoDiv.setAttribute('data-src', newDataSrc)
    }
  }

  // 处理video元素的src属性
  const videoElements = doc.querySelectorAll('video')
  for (const video of videoElements) {
    if (video.src && isFileUrl(video.src)) {
      const errorMsg = isCloudMode ? '发布视频上传失败' : '发布视频处理失败'
      const newSrc = await processFileUrl(video.src, errorMsg)
      if (isCloudMode && uploadedFiles.has(fileUrl2Path(video.src))) {
        video.setAttribute('filekey', uploadedFiles.get(fileUrl2Path(video.src))!)
      }
      video.setAttribute('src', newSrc)
    }
  }

  // 处理source元素的src属性
  const sourceElements = doc.querySelectorAll('source')
  for (const source of sourceElements) {
    if (source.src && isFileUrl(source.src)) {
      const errorMsg = isCloudMode ? '发布视频上传失败' : '发布视频处理失败'
      const newSrc = await processFileUrl(source.src, errorMsg)
      if (isCloudMode && uploadedFiles.has(fileUrl2Path(source.src))) {
        source.setAttribute('filekey', uploadedFiles.get(fileUrl2Path(source.src))!)
      }
      source.setAttribute('src', newSrc)
    }
  }
}

/**
 * 推送服务，这个服务用于对外提供完整的推送任务聚合，依赖数据库、云端、状态
 * TODO 这个服务貌似越来越臃肿，考虑分解它
 */
export function usePushingService() {
  const { userInfo } = useUserInfo()
  const pushingTaskSetApi = usePushingTaskSetApi()
  const pushingTaskApi = usePushingTaskApi()
  const pushingTaskStore = usePushingTaskStore()
  const pushingTaskSetOperatorStore = usePushingTaskSetOperatorStore()
  const pushingTaskAccountStore = usePushingTaskAccountStore()
  const pushingTaskSetReportingStateStore = usePushingTaskSetReportingStateStore()
  const pushingTaskPushResultStore = usePushingTaskPushResultStore()
  const platformAuditResultStore = usePlatformAuditResultStore()
  const schedulingPlatformStateQueryStore = useSchedulingPlatformStateQueryStore()
  const pushingConfigStore = usePushingConfigStore()
  const transactionService = useDBTransaction()
  const authorizeService = useAuthorizeService()
  const pushingTaskSetStore = usePushingTaskSetStore()
  const publishStore = usePublishStore()
  const featureManager = useFeatureManager()
  const { logPublishScheduling } = usePersistentLogService()
  const assetLibraryService = useAssetLibraryService()
  const wechat3rdPartyService = useWechat3rdPartyService()
  const publishFileService = usePublishFileService()
  const localFileService = useLocalFileService()

  const updateProgress = useCallback(
    async (taskId: string, progress: number, message: string) => {
      const state = await pushingTaskPushResultStore.get(taskId)
      state.updateProgress(message)
      await pushingTaskPushResultStore.save(state)
      eventBus.emit(pushEvents.pushingProgressChanged, { taskId, progress })
    },
    [pushingTaskPushResultStore],
  )

  const succeedTask = useCallback(
    async (taskId: string, publishId: string, wechatLockToken: [string, string] | null) => {
      const taskPushResult = await pushingTaskPushResultStore.get(taskId)
      taskPushResult.succeed(publishId)
      await pushingTaskPushResultStore.save(taskPushResult)
      eventBus.emit(pushEvents.pushResultChanged, taskPushResult.taskId)
      eventBus.emit(pushEvents.pushTaskCompleted, {
        taskId: taskPushResult.taskId,
        wechatLockToken,
      })
      await platformAuditResultStore.save(new PlatformAuditResult(taskId))
    },
    [pushingTaskPushResultStore, platformAuditResultStore],
  )

  const failTask = useCallback(
    async (taskId: string, message: string, wechatLockToken: [string, string] | null) => {
      const taskPushResult = await pushingTaskPushResultStore.get(taskId)
      taskPushResult.fail(message)
      await pushingTaskPushResultStore.save(taskPushResult)
      eventBus.emit(pushEvents.pushResultChanged, taskPushResult.taskId)
      eventBus.emit(pushEvents.pushTaskCompleted, {
        taskId: taskPushResult.taskId,
        wechatLockToken,
      })
    },
    [pushingTaskPushResultStore],
  )

  const startLocalTask = useCallback(
    async (taskSetId: string) => {
      const res = await pushingTaskSetApi.getPublishForm(taskSetId)

      platformService.pushAppTask(res)
    },
    [pushingTaskSetApi],
  )

  const startPush = useCallback(
    async (
      tasks: PushingTask[],
      toDraft: boolean,
      wechatLockToken: Map<string, string> = new Map(),
    ) => {
      for (const task of tasks) {
        try {
          const account = await authorizeService.getSpiderAccount(task.accountId)
          let session: AccountSession
          let wechatToken: [string, string] | null = null
          if (account instanceof WechatShiPinHao3rdPartySubAccount) {
            // 三方授权账号不需要预先取session，在执行时获取
            session = {
              cookies: [],
              localStorage: {},
            } as AccountSession
            const token = wechatLockToken.get(account.parentAccountId)
            wechatToken = token ? [account.parentAccountId, token] : null // 按照业务来说，界面应该确保对应Token存在
          } else {
            const { session: accountSession } = await authorizeService.getAccountSession(account)
            session = accountSession
          }

          const sourcePushingConfig = await pushingConfigStore.getConfig(task.pushingConfigId!)
          let pushingConfig: LocalPushingConfig
          if (sourcePushingConfig instanceof CloudVideoPushingConfig) {
            const takeOutPushResult = await pushingTaskPushResultStore.get(task.taskId)
            takeOutPushResult.state = '视频上传中'
            await pushingTaskPushResultStore.save(takeOutPushResult)
            eventBus.emit(pushEvents.pushResultChanged, task.taskId)
            task.content = await electronService.downloadFileLocal(
              sourcePushingConfig.videoUrl,
              'video',
            )
            const videoInfo = await localFileService.getVideoFileInfo(task.content)
            //TODO 将这个分类持久化并且到后续的流程中使用
            const contentType = contentTypeClassifier.classify(account.platform.name, videoInfo)

            const cover = await electronService.getImageFileInfo(task.thumbUrl)
            pushingConfig = new LocalVideoPushingConfig(
              sourcePushingConfig.configId,
              sourcePushingConfig.accountId,
              sourcePushingConfig.platform,
              sourcePushingConfig.locationKeyword,
              null,
              sourcePushingConfig.isOriginal,
              contentType,
              sourcePushingConfig.title,
              sourcePushingConfig.description,
              videoInfo,
              cover,
              [],
              toDraft,
            )
          } else {
            pushingConfig = sourcePushingConfig
          }

          const body = await bodyConverter.convert(
            task.taskId,
            task.platform.name,
            task.pushContentType,
            account,
            session,
            pushingConfig,
            toDraft,
          )

          logPublishScheduling(task, account, wechatToken, session, body)
          await platformService.pushToPlatform(task, account, wechatToken, session, body)
        } catch (e) {
          console.error(e)
          const message = e instanceof Error ? e.message : JSON.stringify(e)
          await failTask(task.taskId, message, null)
        }
      }
    },
    [
      authorizeService,
      pushingConfigStore,
      logPublishScheduling,
      pushingTaskPushResultStore,
      localFileService,
      failTask,
    ],
  )

  const startSuperLock = useCallback(
    async ({ lockId, activityId }: { lockId: string; activityId: string }) => {
      const superdir = useInnerContextStore
        .getState()
        .currentTeam?.components?.find((c) => c.name === 'superdir')

      if (!superdir || !superdir.componentArgs?.token) {
        throw new Error('无效Token')
      }

      const res = await axios.post(
        `${import.meta.env.VITE_SUPER_API_URL}/v1/openapi/matrix/video/lock`,
        {
          lock_source: 'douyin_yixiaoer',
          lock_id: lockId,
          activity_id: Math.floor(Math.random() * 9000000) + 1000000,
          status: 2,
        },
        {
          headers: {
            Token: superdir.componentArgs.token as string,
          },
        },
      )

      if (res.data.code !== 0) {
        throw new Error(res.data.msg || res.data.message)
      }
    },
    [],
  )

  const startSuperOccupy = useCallback(
    async ({ lockId, videoIds }: { lockId: string; videoIds: string[] }) => {
      const superdir = useInnerContextStore
        .getState()
        .currentTeam?.components?.find((c) => c.name === 'superdir')

      if (!superdir || !superdir.componentArgs?.token) {
        throw new Error('无效Token')
      }

      const res = await axios.post(
        `${import.meta.env.VITE_SUPER_API_URL}/v1/openapi/matrix/video/hold`,
        {
          lock_source: 'douyin_yixiaoer',
          lock_id: lockId,
          video_ids: videoIds,
        },
        {
          headers: {
            Token: superdir.componentArgs.token as string,
          },
        },
      )

      if (res.data.code !== 0) {
        throw new Error(res.data.msg || res.data.message)
      }
    },
    [],
  )

  const resumeUnfinishedPushingTasks = useCallback(async () => {
    const pushResults = await pushingTaskPushResultStore.getAllUnfinishedStates()
    const pushingTasks = await pushingTaskStore.getMany(pushResults.map((x) => x.taskId))
    for (const pushResult of pushResults) {
      pushResult.reset()
      await pushingTaskPushResultStore.save(pushResult)
      eventBus.emit(pushEvents.pushResultChanged, pushResult.taskId)
    }
    console.log('未完成的推送任务：', pushingTasks)
    await startPush(pushingTasks, false)
  }, [pushingTaskPushResultStore, pushingTaskStore, startPush])

  const reportBrowserTask = useCallback(() => pushingTaskApi.reportBrowserTask(), [pushingTaskApi])

  const resumeSchedulingPlatformStateQuery = useCallback(async () => {
    // // 查询未完成的平台状态查询
    // const schedulingTasks = await schedulingPlatformStateQueryStore.getAll()
    // // 查询对应的推送任务
    // const pushingTasks = await pushingTaskStore.getMany(schedulingTasks.map((x) => x.taskId))
    // console.log('未完成的平台状态查询：', schedulingTasks, pushingTasks)
    // for (const scheduling of schedulingTasks) {
    //   const task = pushingTasks.find((x) => x.taskId === scheduling.taskId)!
    //   const account = await accountApi.getAccount(task.accountId)
    //   await platformService.schedulePlatformStateQuery(scheduling.scheduleDatetime, task!, account)
    // }
  }, [])

  // ----------待改造----------
  const succeedQueryAuditResult = useCallback(
    async (taskId: string, result: QueryStateResult) => {
      await schedulingPlatformStateQueryStore.delete(taskId)

      const platformAuditResult = new PlatformAuditResult(
        taskId,
        result.auditStatus,
        result.documentId,
        result.openUrl,
        result.detail,
      )

      await platformAuditResultStore.save(platformAuditResult)

      eventBus.emit(pushEvents.auditResultUpdated, taskId)
    },
    [schedulingPlatformStateQueryStore, platformAuditResultStore],
  )

  const scheduleAuditStateQuery = useCallback(async (taskSetId: PushingTaskSetIdentifier) => {
    await platformService.schedulePlatformStateQueryNew(taskSetId, datetimeService.minutesLater(5))
  }, [])

  const auditStateQuery = useCallback(
    async (taskSetId: PushingTaskSetIdentifier) => {
      const taskSet = await pushingTaskSetApi.getTaskSet(taskSetId)
      if (taskSet.isDraft) {
        return
      }
      const taskViewModelsNeedQuery = (await pushingTaskApi.getTasksByTaskSetId(taskSetId)).filter(
        (x) => needQueryAuditResult(x),
      )
      const accounts = await Promise.all(
        taskViewModelsNeedQuery
          .filter((x) => x.accountId !== undefined)
          .map((x) => authorizeService.getSpiderAccount(x.accountId!)),
      )

      let wechatTokens:
        | {
            platformAccountId: string
            wxkey: string
          }[]
        | null = null
      try {
        wechatTokens = await wechat3rdPartyService.lockParentAccount(
          accounts.filter((x) => x instanceof WechatShiPinHao3rdPartySubAccount),
        )
      } catch (e) {
        // 锁定失败应该不影响其它账号
        if (e instanceof BusinessError) {
          console.error(e.message)
        } else throw e
      }

      try {
        for (const task of taskViewModelsNeedQuery) {
          try {
            const account = accounts.find((x) => x.accountId === task.accountId)
            if (!account) {
              continue
            }

            const result = await platformService.auditStateQuery(
              task.taskId,
              task.platform.name,
              task.editContentType,
              account,
              task.publishId!,
            )

            await succeedQueryAuditResult(task.taskId, result)
          } catch (error) {
            // ignore error
          }
        }
      } finally {
        if (wechatTokens !== null) {
          for (const token of wechatTokens) {
            void wechat3rdPartyService.unlockWechatAccount(token.platformAccountId, token.wxkey)
          }
        }
      }
    },
    [
      authorizeService,
      pushingTaskApi,
      pushingTaskSetApi,
      succeedQueryAuditResult,
      wechat3rdPartyService,
    ],
  )

  const prepareForPush = useCallback(
    async (pushingConfigs: AllPushingConfig[], allAccounts: Account[]) => {
      if (pushingConfigs.length === 0) {
        throw new Error('没有推送配置')
      }
      const tasks: PushingTask[] = []
      const taskSet: PushingTaskSet = PushingTaskSet.fromLocalPush(
        identifierService.generateUUIDnew<PushingTaskSetIdentifier>(),
        Array.from(new Set(pushingConfigs.map((x) => x.platform.name))),
        datetimeService.now(),
      )

      const coverFilePath =
        pushingConfigs[0] instanceof LocalImageTextPushingConfig // 图文使用第一张图片作为封面
          ? pushingConfigs[0].images[0].path
          : pushingConfigs[0].cover?.path
      let thumbUrl = coverFilePath!
      if (coverFilePath) {
        const buffer = await assetLibraryService.readFile(coverFilePath)
        const uploadRes = await assetLibraryService.uploadImageAsset(`cover_${Date.now()}`, buffer)
        if (uploadRes) {
          thumbUrl = uploadRes
        }
      }

      for (const [index, pushingConfig] of pushingConfigs.entries()) {
        if (index === 0) {
          taskSet.contentType = pushingConfig.editContentType
          taskSet.thumbUrl = thumbUrl
          taskSet.brief = htmlService.extractTextFromHtml(
            pushingConfig instanceof LocalArticlePushingConfig
              ? pushingConfig.title
              : pushingConfig.description,
          )
        }
        let taskId: string
        if (pushingConfig instanceof CloudVideoPushingConfig) {
          taskId = pushingConfig?.appTaskId
        } else {
          taskId = identifierService.generateUUID()
        }
        const task = PushingTask.fromConfig(
          taskId,
          pushingConfig.platform,
          pushingConfig.accountId,
          pushingConfig.editContentType,
          pushingConfig.pushContentType,
          pushingConfig.configId,
          pushingConfig instanceof LocalImageTextPushingConfig ||
            pushingConfig instanceof LocalArticlePushingConfig
            ? ''
            : pushingConfig.videoUrl || '',
          pushingConfig?.cover?.path || '',
          htmlService.extractTextFromHtml(
            pushingConfig instanceof LocalArticlePushingConfig
              ? pushingConfig.title
              : pushingConfig.description,
          ),
          datetimeService.now(),
          pushingConfig.timing,
        )

        tasks.push(task)
      }
      const publish = new Publish(taskSet, tasks)
      await transactionService.withTransaction(async () => {
        await publishStore.save(publish)
        await pushingTaskSetStore.save(taskSet)
        await pushingTaskSetOperatorStore.save(
          new PushingTaskSetOperator(taskSet.taskSetId, userInfo.id, userInfo.nickName),
        )
        await pushingTaskSetReportingStateStore.markAsUnreported(taskSet.taskSetId)
        for (const task of tasks) {
          const account = allAccounts.find((x) => x.accountId === task.accountId)!
          await pushingTaskStore.save(task)
          await pushingTaskAccountStore.save(
            task.taskId,
            task.accountId,
            account.nickName,
            account.remark ?? '',
            account.avatar,
          )
          await pushingTaskPushResultStore.save(new PushingTaskPushResult(task.taskId))
        }
      })
      eventBus.emit(pushEvents.pushingTaskPrepared)
      void scheduleAuditStateQuery(taskSet.taskSetId)
      return tasks
    },
    [
      transactionService,
      scheduleAuditStateQuery,
      assetLibraryService,
      publishStore,
      pushingTaskSetStore,
      pushingTaskSetOperatorStore,
      userInfo.id,
      userInfo.nickName,
      pushingTaskSetReportingStateStore,
      pushingTaskStore,
      pushingTaskAccountStore,
      pushingTaskPushResultStore,
    ],
  )

  const cancelCloudTask = useCallback(
    async (taskId: string) => {
      await pushingTaskApi.cancelTask(taskId)
    },
    [pushingTaskApi],
  )

  const cancelLocalTask = useCallback(
    async (taskId: string) => {
      await platformService.cancelPublishTask(taskId)
    },
    [platformService],
  )

  const getUnfinishedTaskIds = useCallback(async () => {
    return await platformService.getUnfinishedTaskIds()
  }, [platformService])

  const deleteTask = useCallback(
    async (taskId: string) => {
      await pushingTaskApi.deleteTask(taskId)
    },
    [pushingTaskApi],
  )

  const deletePushingTask = useCallback(
    async (taskSetId: PushingTaskSetIdentifier[] | PushingTaskSetIdentifier) => {
      await transactionService.withTransaction(async () => {
        // 获取任务集中的所有任务
        const taskIds = await publishStore.getTaskIds(taskSetId)
        const tasks = await pushingTaskStore.getMany(taskIds)
        await publishStore.removeByTaskSetId(taskSetId)
        await pushingTaskStore.deleteTasks(taskIds)
        await pushingTaskPushResultStore.delete(taskIds)
        await pushingTaskAccountStore.delete(taskIds)
        await pushingConfigStore.deleteConfigs(tasks.map((x) => x.pushingConfigId!))
        await platformAuditResultStore.deleteMany(taskIds)
        await schedulingPlatformStateQueryStore.delete(taskIds)
        await pushingTaskSetOperatorStore.delete(taskSetId)
        await pushingTaskSetReportingStateStore.delete(taskSetId)
        await pushingTaskSetStore.delete(taskSetId)
      })
      eventBus.emit(pushEvents.pushingTaskDeleted, taskSetId)
    },
    [
      transactionService,
      publishStore,
      pushingTaskStore,
      pushingTaskPushResultStore,
      pushingTaskAccountStore,
      pushingConfigStore,
      platformAuditResultStore,
      schedulingPlatformStateQueryStore,
      pushingTaskSetOperatorStore,
      pushingTaskSetReportingStateStore,
      pushingTaskSetStore,
    ],
  )

  const getCloudPushingTaskSetsViewModel = useCallback(
    (
      pushState: PushSetStateFilterOption,
      operators: Operator[],
      contentType: ContentTypeFilterOption,
      cursor: Date | null,
    ) => {
      return pushingTaskSetApi.getTaskSets(pushState, operators, contentType, cursor)
    },
    [pushingTaskSetApi],
  )

  const reportTasks = useCallback(
    async (toDraft?: boolean) => {
      const taskSetIds = await pushingTaskSetReportingStateStore.getUnreportedTaskSetIds()
      for (const taskSetId of taskSetIds) {
        try {
          const publish = await publishStore.getByTaskSetId(taskSetId)
          const taskIds = publish.tasks.map((x) => x.taskId)
          const tasks = await pushingTaskStore.getMany(taskIds)
          const pushResults = await pushingTaskPushResultStore.getAll(taskIds)
          const auditResults = await platformAuditResultStore.getMany(taskIds)
          const account = await pushingTaskAccountStore.getMany(taskIds)

          const report = {
            taskSet: publish.taskSet,
            tasks: tasks.map((task) => {
              const pushResult = pushResults.find((x) => x.taskId === task.taskId)
              if (!pushResult) {
                throw new Error(`PushResult not found: ${task.taskId}`)
              }
              const auditResult = auditResults.find((x) => x.taskId === task.taskId)
              const accountInfo = account.find((x) => x.taskId === task.taskId)

              return {
                task: task,
                pushResult: pushResult,
                auditResult: auditResult ?? null,
                account: accountInfo,
              }
            }),
          }
          await pushingTaskSetApi.reportTaskSets(report, toDraft)
          await pushingTaskSetReportingStateStore.markAsReported(taskSetId)
        } catch (e) {
          console.error(e)
        }
      }
    },
    [
      pushingTaskSetReportingStateStore,
      publishStore,
      pushingTaskStore,
      pushingTaskPushResultStore,
      platformAuditResultStore,
      pushingTaskAccountStore,
      pushingTaskSetApi,
    ],
  )

  // ------待改造----------
  const updateCloudPushResult = useCallback(
    async (taskId: string) => {
      const pushResult = await pushingTaskPushResultStore.get(taskId)
      const task = await pushingTaskStore.get(taskId)
      const pushConfig = await pushingConfigStore.getConfig(task.pushingConfigId!)

      let token: string | undefined = undefined
      if (pushConfig instanceof CloudVideoPushingConfig) {
        token = pushConfig.token
      }
      let stageStatus = PlatformResultStageStatus.DOING
      let stages = PlatformResultStage.UPLOAD

      switch (pushResult.state) {
        case '视频上传中':
          stages = PlatformResultStage.UPLOAD
          break
        case '等待推送':
          stages = PlatformResultStage.PUSH
          break
        case '正在推送':
          stages = PlatformResultStage.PUSH
          break
        case '推送成功':
          stages = PlatformResultStage.PUSH
          stageStatus = PlatformResultStageStatus.SUCCESS
          break
        case '推送失败':
          stages = PlatformResultStage.PUSH
          stageStatus = PlatformResultStageStatus.FAIL
          break
        default:
          throw new Error(`不支持的API数据转换:推送状态:${pushResult.state}`)
      }

      void pushingTaskApi
        .updateTaskStatus(
          taskId,
          {
            message: pushResult.message,
            publishId: pushResult.publishId,
            stageStatus: stageStatus,
            stages: stages,
            documentId: null,
            openUrl: null,
          },
          token,
        )
        .then(() => {
          eventBus.emit(pushEvents.cloudPushResultChanged, taskId)
        })
    },
    [pushingTaskPushResultStore, pushingTaskStore, pushingConfigStore, pushingTaskApi],
  )

  const publishCloudForArticle = useCallback(
    async (
      config: ArticlePublishViewModel,
      validAccounts: Account[],
      toDraft: boolean,
      publishChannel: 'local' | 'cloud',
    ) => {
      let coverKey = ''

      if (config.cover) {
        const coverBuffer = await assetLibraryService.readFile(config.cover.path)

        const _coverKey = await assetLibraryService.uploadImageAsset(
          `cover_${Date.now()}`,
          coverBuffer,
        )

        if (!_coverKey) {
          notifyService.error('封面上传失败')
          throw new Error('封面上传失败')
        }

        coverKey = _coverKey
      }

      const formData = convertArticleFormData(config, toDraft)
      formData.accounts = validAccounts.map((x) => ({
        accountId: x.accountId,
      }))

      if (publishChannel === 'cloud') {
        const parser = new DOMParser()
        const doc = parser.parseFromString(config.content, 'text/html')

        await processMediaFiles(doc, true, publishFileService, notifyService)

        if (config.cover) {
          let publish_cover_key: string
          try {
            publish_cover_key = await publishFileService.uploadFile(config.cover.path)
          } catch (e) {
            notifyService.error('发布封面上传失败')
            throw e
          }
          formData.cover = {
            key: publish_cover_key,
            width: config.cover!.width,
            height: config.cover!.height,
            size: config.cover!.size,
          }
        }

        if (config.verticalCover) {
          let publish_cover_key: string
          try {
            publish_cover_key = await publishFileService.uploadFile(config.verticalCover.path)
          } catch (e) {
            notifyService.error('发布封面上传失败')
            throw e
          }
          formData.verticalCover = {
            key: publish_cover_key,
            width: config.verticalCover!.width,
            height: config.verticalCover!.height,
            size: config.verticalCover!.size,
          }
        }

        formData.content = doc.body.innerHTML

        const taskSetId = await pushingTaskSetApi.publishTaskSet({
          coverKey: coverKey,
          desc: config.title,
          isDraft: toDraft,
          isTimed: config.timing,
          platformAccounts: config.accounts.map(
            (account) =>
              ({
                platformAccountId: account.accountId,
              }) satisfies PublishAccountRequest,
          ),
          platforms: Array.from(new Set(validAccounts.map((account) => account.platform.name))),
          publishType: EditContentType.Article,
          publishChannel,
          publishArgs: formData,
        })
        return taskSetId
      }

      if (publishChannel === 'local') {
        const parser = new DOMParser()
        const doc = parser.parseFromString(config.content, 'text/html')

        await processMediaFiles(doc, false, publishFileService, notifyService)

        formData.content = doc.body.innerHTML
      }
      formData.cover = {
        path: config.cover?.path,
        width: config.cover?.width ?? 0,
        height: config.cover?.height ?? 0,
        size: config.cover?.size ?? 0,
      }

      formData.verticalCover = {
        path: config.verticalCover?.path,
        width: config.verticalCover?.width ?? 0,
        height: config.verticalCover?.height ?? 0,
        size: config.verticalCover?.size ?? 0,
      }

      const taskSetId = await pushingTaskSetApi.publishTaskSet({
        coverKey: coverKey,
        desc: config.title,
        isDraft: toDraft,
        isTimed: config.timing,
        platformAccounts: config.accounts.map(
          (account) =>
            ({
              platformAccountId: account.accountId,
            }) satisfies PublishAccountRequest,
        ),
        platforms: Array.from(new Set(validAccounts.map((account) => account.platform.name))),
        publishType: EditContentType.Article,
        publishChannel,
        publishArgs: formData,
      })
      return taskSetId
    },
    [assetLibraryService, publishFileService, pushingTaskSetApi],
  )

  // 图文
  const publishCloudForImageText = useCallback(
    async (
      config: ImageTextPublishViewModel,
      validAccounts: Account[],
      toDraft: boolean,
      publishChannel: 'local' | 'cloud',
    ) => {
      const coverPath = config.cover ? config.cover.path : config.images[0].path
      const coverBuffer = await assetLibraryService.readFile(coverPath)
      const coverKey = await assetLibraryService.uploadImageAsset(
        `cover_${Date.now()}`,
        coverBuffer,
      )
      if (!coverKey) {
        notifyService.error('封面上传失败')
        throw new Error('封面上传失败')
      }

      const formData = convertImageTextFormData(config, toDraft)

      if (publishChannel === 'cloud') {
        const publishKeys = {}
        for (const item of config.images) {
          try {
            publishKeys[item.path] = await publishFileService.uploadFile(item.path)
          } catch (e) {
            notifyService.error('发布图片上传失败')
            throw e
          }
        }

        let publish_cover_key: string
        try {
          publish_cover_key = await publishFileService.uploadFile(coverPath)
        } catch (e) {
          notifyService.error('发布封面上传失败')
          throw e
        }

        formData.images = config.images.map((x) => ({ key: publishKeys[x.path], ...x }))
        formData.cover = {
          key: publish_cover_key,
          width: config.cover?.width ?? 0,
          height: config.cover?.height ?? 0,
          size: config.cover?.size ?? 0,
        }
        formData.accounts = validAccounts.map((x) => ({ accountId: x.accountId }))

        const taskSetId = await pushingTaskSetApi.publishTaskSet({
          coverKey: coverKey,

          desc: config.title,
          isDraft: toDraft,
          isTimed: config.timing,
          platformAccounts: config.accounts.map(
            (account) =>
              ({
                platformAccountId: account.accountId,
              }) satisfies PublishAccountRequest,
          ),
          platforms: Array.from(new Set(validAccounts.map((account) => account.platform.name))),
          publishType: EditContentType.ImageText,
          publishChannel,
          publishArgs: formData,
        })
        return taskSetId
      }

      formData.images = config.images
      formData.cover = {
        path: config.cover?.path,
        width: config.cover?.width ?? 0,
        height: config.cover?.height ?? 0,
        size: config.cover?.size ?? 0,
      }
      formData.accounts = validAccounts.map((x) => ({ accountId: x.accountId }))

      const taskSetId = await pushingTaskSetApi.publishTaskSet({
        coverKey: coverKey,
        desc: htmlService.extractTextFromHtml(config.description),
        isDraft: toDraft,
        isTimed: config.timing,
        platformAccounts: config.accounts.map(
          (account) =>
            ({
              coverKey: coverKey,
              platformAccountId: account.accountId,
            }) satisfies PublishAccountRequest,
        ),
        platforms: Array.from(new Set(validAccounts.map((account) => account.platform.name))),
        publishType: EditContentType.ImageText,
        publishChannel,
        publishArgs: formData,
      })
      return taskSetId
    },
    [assetLibraryService, publishFileService, pushingTaskSetApi],
  )

  const publishCloudForSingleVideo = useCallback(
    async (
      accountConfig: SingleVideoAccountViewModel,
      contentConfig: VideoContentViewModel,
      toDraft: boolean,
      publishChannel: 'local' | 'cloud',
    ) => {
      const cover = accountConfig.cover
      const video = accountConfig.video
      if (!video || !cover) throw new Error('没有视频或封面')

      const coverBuffer = await assetLibraryService.readFile(cover.path)
      const coverKey = await assetLibraryService.uploadImageAsset(
        `cover_${Date.now()}`,
        coverBuffer,
      )
      if (!coverKey) {
        notifyService.error('封面上传失败')
        throw new Error('封面上传失败')
      }

      // 云端任务 region 上传
      if (publishChannel === 'cloud') {
        let publish_cover_key: string
        try {
          publish_cover_key = await publishFileService.uploadFile(cover.path)
        } catch (e) {
          notifyService.error('发布封面上传失败')
          throw e
        }

        let videoKey: string
        try {
          videoKey = await publishFileService.uploadFile(video.filePath)
        } catch (e) {
          notifyService.error('发布视频上传失败')
          throw e
        }

        // endregion

        const formData = convertVideoFormData(contentConfig, toDraft)
        formData.accounts = accountConfig.accounts.map((account) => ({
          accountId: account.accountId,
          cover: {
            key: publish_cover_key,
            width: cover.width,
            height: cover.height,
            size: cover.byteSize.bytes,
          } satisfies ImageFormItem,
          video: {
            key: videoKey,
            duration: video.fileDurationTimeSpan.seconds,
            width: video.videoWidth,
            height: video.videoHeight,
            size: video.fileByteSize.bytes,
          } satisfies VideoFormItem,
        }))
        const taskSetId = await pushingTaskSetApi.publishTaskSet({
          coverKey: coverKey,
          desc: htmlService.extractTextFromHtml(
            contentConfig.aPlatformForm.description ?? contentConfig.bPlatformForm.description,
          ),
          isDraft: toDraft,
          isTimed: contentConfig.timing,
          platformAccounts: accountConfig.accounts.map(
            (account) =>
              ({
                videoKey: videoKey,
                coverKey: publish_cover_key,
                platformAccountId: account.accountId,
                superLockId: video.superLockId,
                superId: video.superId,
              }) satisfies PublishAccountRequest,
          ),
          platforms: Array.from(
            new Set(accountConfig.accounts.map((account) => account.platform.name)),
          ),
          publishType: EditContentType.Video,
          publishChannel,
          publishArgs: formData,
        })

        return taskSetId
      }

      const formData = convertVideoFormData(contentConfig, toDraft)

      formData.accounts = accountConfig.accounts.map((account) => ({
        accountId: account.accountId,
        cover: {
          path: cover.path,
          width: cover.width,
          height: cover.height,
          size: cover.byteSize.bytes,
        } satisfies ImageFormItem,
        video: {
          path: video.filePath,
          duration: video.fileDurationTimeSpan.seconds,
          width: video.videoWidth,
          height: video.videoHeight,
          size: video.fileByteSize.bytes,
        } satisfies VideoFormItem,
      }))
      const taskSetId = await pushingTaskSetApi.publishTaskSet({
        coverKey: coverKey,
        desc: htmlService.extractTextFromHtml(
          contentConfig.aPlatformForm.description ?? contentConfig.bPlatformForm.description,
        ),
        isDraft: toDraft,
        isTimed: contentConfig.timing,
        platformAccounts: accountConfig.accounts.map(
          (account) =>
            ({
              videoKey: video.filePath,
              coverKey: cover.path,
              platformAccountId: account.accountId,
              superLockId: video.superLockId,
              superId: video.superId,
            }) satisfies PublishAccountRequest,
        ),
        platforms: Array.from(
          new Set(accountConfig.accounts.map((account) => account.platform.name)),
        ),
        publishType: EditContentType.Video,
        publishChannel,
        publishArgs: formData,
      })

      return taskSetId
    },
    [assetLibraryService, publishFileService, pushingTaskSetApi],
  )

  const publishCloudForMultipleVideo = useCallback(
    async (
      accountConfig: MultipleVideoAccountViewModel,
      contentConfig: VideoContentViewModel,
      toDraft: boolean,
      publishChannel: 'cloud' | 'local',
    ) => {
      const firstCover = accountConfig.items?.[0]?.cover
      if (!firstCover) throw new Error('没有封面')

      const coverBuffer = await assetLibraryService.readFile(firstCover.path)
      const coverKey = await assetLibraryService.uploadImageAsset(
        `cover_${Date.now()}`,
        coverBuffer,
      )
      if (!coverKey) {
        notifyService.error('封面上传失败')
        throw new Error('封面上传失败')
      }

      if (publishChannel === 'cloud') {
        const publish_keys: {
          index: number
          coverKey: string
          videoKey: string
        }[] = []
        for (let index = 0; index < accountConfig.items.length; index++) {
          const item = accountConfig.items[index]
          const cover = item.cover
          const video = item.video
          if (!video || !cover) throw new Error('没有视频或封面')

          // 先上传封面
          let coverKey: string
          try {
            coverKey = await publishFileService.uploadFile(cover.path)
          } catch (e) {
            notifyService.error('发布封面上传失败')
            throw e
          }

          // 再上传视频
          let videoKey: string
          try {
            videoKey = await publishFileService.uploadFile(video.filePath)
          } catch (e) {
            notifyService.error('发布视频上传失败')
            throw e
          }

          publish_keys.push({ index, coverKey, videoKey })
        }

        const platformAccounts: PublishAccountRequest[] = accountConfig.items.map(
          (item, index) =>
            ({
              coverKey: publish_keys[index].coverKey,
              platformAccountId: item.account!.accountId,
              videoKey: publish_keys[index].videoKey,
              superId: item.video?.superId,
              superLockId: item.video?.superLockId,
            }) satisfies PublishAccountRequest,
        )

        if (platformAccounts.length === 0) throw new Error('没有有效的账号配置')

        const formData = convertVideoFormData(contentConfig, toDraft)
        formData.accounts = accountConfig.items.map((item, index) => ({
          accountId: item.account!.accountId,
          cover: {
            key: publish_keys[index].coverKey,
            width: item.cover!.width,
            height: item.cover!.height,
            size: item.cover!.byteSize.bytes,
          } satisfies ImageFormItem,
          video: {
            key: publish_keys[index].videoKey,
            duration: item.video!.fileDurationTimeSpan.seconds,
            width: item.video!.videoWidth,
            height: item.video!.videoHeight,
            size: item.video!.fileByteSize.bytes,
          } satisfies VideoFormItem,
        }))
        const taskSetId = await pushingTaskSetApi.publishTaskSet({
          coverKey: coverKey,
          desc: htmlService.extractTextFromHtml(
            contentConfig.aPlatformForm.description ?? contentConfig.bPlatformForm.description,
          ),
          isDraft: toDraft,
          isTimed: contentConfig.timing,
          platformAccounts: platformAccounts,
          platforms: Array.from(
            new Set(accountConfig.items.map((item) => item.account!.platform.name)),
          ),
          publishType: EditContentType.Video,
          publishChannel,
          publishArgs: formData,
        })

        return taskSetId
      }

      const platformAccounts: PublishAccountRequest[] = accountConfig.items.map(
        (item) =>
          ({
            coverKey: item.cover!.path,
            platformAccountId: item.account!.accountId,
            videoKey: item.video!.filePath,
            superId: item.video?.superId,
            superLockId: item.video?.superLockId,
          }) satisfies PublishAccountRequest,
      )

      if (platformAccounts.length === 0) throw new Error('没有有效的账号配置')

      const formData = convertVideoFormData(contentConfig, toDraft)
      formData.accounts = accountConfig.items.map((item) => ({
        accountId: item.account!.accountId,
        cover: {
          path: item.cover!.path,
          width: item.cover!.width,
          height: item.cover!.height,
          size: item.cover!.byteSize.bytes,
        } satisfies ImageFormItem,
        video: {
          path: item.video!.filePath,
          duration: item.video!.fileDurationTimeSpan.seconds,
          width: item.video!.videoWidth,
          height: item.video!.videoHeight,
          size: item.video!.fileByteSize.bytes,
        } satisfies VideoFormItem,
      }))
      const taskSetId = await pushingTaskSetApi.publishTaskSet({
        coverKey: coverKey,
        desc: htmlService.extractTextFromHtml(
          contentConfig.aPlatformForm.description ?? contentConfig.bPlatformForm.description,
        ),
        isDraft: toDraft,
        isTimed: contentConfig.timing,
        platformAccounts: platformAccounts,
        platforms: Array.from(
          new Set(accountConfig.items.map((item) => item.account!.platform.name)),
        ),
        publishType: EditContentType.Video,
        publishChannel,
        publishArgs: formData,
      })

      return taskSetId
    },
    [assetLibraryService, publishFileService, pushingTaskSetApi],
  )

  const generatePushingConfigsForSingleVideo = useCallback(
    async (
      accountConfig: SingleVideoAccountViewModel,
      contentConfig: VideoContentViewModel,
      validAccounts: Account[],
      toDraft: boolean,
    ) => {
      const pushingConfigs = validAccounts.map((account) => {
        return LocalVideoPushingConfig.fromViewModel(
          identifierService.generateUUID(),
          account.accountId,
          account.platform,
          '',
          contentConfig.location,
          contentConfig.isOriginal,
          contentTypeClassifier.classify(account.platform.name, accountConfig.video!),
          isModernPlatform(account.platform)
            ? contentConfig.aPlatformForm.title
            : contentConfig.bPlatformForm.title,
          isModernPlatform(account.platform)
            ? contentConfig.aPlatformForm.description
            : contentConfig.bPlatformForm.description,
          accountConfig.video!,
          accountConfig.cover!,
          contentConfig.bPlatformForm.tags,
          toDraft,
          contentConfig.timing,
          contentConfig.categories,
        )
      })
      await pushingConfigStore.saveConfigs(pushingConfigs)
      return pushingConfigs
    },
    [pushingConfigStore],
  )

  const generatePushingConfigsForMultipleVideo = useCallback(
    async (
      accountConfig: MultipleVideoAccountViewModel,
      contentConfig: VideoContentViewModel,
      validAccounts: Account[],
      toDraft: boolean,
    ) => {
      const pushingConfigs = accountConfig.items
        .filter((item) =>
          validAccounts.some((account) => account.accountId === item.account?.accountId),
        )
        .map((item) => {
          return LocalVideoPushingConfig.fromViewModel(
            identifierService.generateUUID(),
            item.account!.accountId,
            item.account!.platform,
            '',
            contentConfig.location,
            contentConfig.isOriginal,
            contentTypeClassifier.classify(item.account!.platform.name, item.video!),
            isModernPlatform(item.account!.platform)
              ? contentConfig.aPlatformForm.title
              : contentConfig.bPlatformForm.title,
            isModernPlatform(item.account!.platform)
              ? contentConfig.aPlatformForm.description
              : contentConfig.bPlatformForm.description,
            item.video!,
            item.cover!,
            contentConfig.bPlatformForm.tags,
            toDraft,
            contentConfig.timing,
            contentConfig.categories,
          )
        })
      await pushingConfigStore.saveConfigs(pushingConfigs)
      return pushingConfigs
    },
    [pushingConfigStore],
  )

  const generatePushingConfigsForImageText = useCallback(
    async (config: ImageTextPublishViewModel, validAccounts: Account[]) => {
      const pushingConfigs = validAccounts.map((account) => {
        return new LocalImageTextPushingConfig(
          identifierService.generateUUID(),
          account.accountId,
          account.platform,
          '',
          config.location,
          PushContentType.ImageText,
          config.title,
          config.description,
          config.music,
          config.images,
          config.cover!,
          config.timing,
        )
      })
      await pushingConfigStore.saveConfigs(pushingConfigs)
      return pushingConfigs
    },
    [pushingConfigStore],
  )

  const generatePushingConfigsForArticle = useCallback(
    async (config: ArticlePublishViewModel, validAccounts: Account[], toDraft: boolean) => {
      const pushingConfigs = validAccounts.map((account) => {
        return LocalArticlePushingConfig.fromViewModel(
          identifierService.generateUUID(),
          account.accountId,
          account.platform,
          PushContentType.Article,
          config.title,
          config.cover,
          config.content,
          config.isFirst,
          config.category,
          config.verticalCover,
          config.locationKeyword,
          config.timing ?? null,
          config.topic,
          toDraft,
        )
      })
      await pushingConfigStore.saveConfigs(pushingConfigs)
      return pushingConfigs
    },
    [pushingConfigStore],
  )

  const rePushTaskSet = useCallback(
    async (taskSet: PushingTaskSetViewModel, all: boolean) => {
      let tasks = await pushingTaskApi.getTasksByTaskSetId(taskSet.taskSetId)
      if (!all) {
        tasks = tasks.filter((x) => x.stageStatus === PlatformResultStageStatus.FAIL)
      }

      if (tasks.length === 0) {
        notifyService.error('没有可重发的任务')
        return
      }

      const accounts: SpiderAccount[] = []

      for (const task of tasks) {
        if (!task.accountId) continue
        try {
          const account = await authorizeService.getSpiderAccount(task.accountId)
          accounts.push(account)
        } catch (e) {
          console.error(e)
        }
      }

      try {
        const wechatTokens = await wechat3rdPartyService.lockParentAccount(
          accounts.filter((x) => x instanceof WechatShiPinHao3rdPartySubAccount),
        )

        const formData = await pushingTaskSetApi.getFormData(taskSet.taskSetId)

        const params = {
          accountsWithTokens: {
            accounts: accounts,
            tokens: new Map<string, string>(
              wechatTokens.map((x) => [x.platformAccountId, x.wxkey]),
            ),
          },
          formData: formData,
        } satisfies PublishFeatureParams
        //TODO 阻止app任务重发
        switch (taskSet.contentType) {
          case EditContentType.Video:
            featureManager.openFeature(features.视频发布, params)
            break
          case EditContentType.ImageText:
            featureManager.openFeature(features.发布图文, params)
            break
          case EditContentType.Article:
            featureManager.openFeature(features.发布文章, params)
            break
        }
      } catch (e) {
        if (e instanceof BusinessError) {
          notifyService.error(e.message)
        }
        throw e
      }
    },
    [authorizeService, featureManager, pushingTaskApi, pushingTaskSetApi, wechat3rdPartyService],
  )

  const updatePushingTaskSetState = useCallback(
    async (taskId: string) => {
      const { taskSetId, taskIds } = await publishStore.getIdsInSameSet(taskId)
      const taskSet = await pushingTaskSetStore.get(taskSetId)
      const pushResults = await pushingTaskPushResultStore.getAll(taskIds)
      const auditResults = await platformAuditResultStore.getMany(taskIds)
      const taskStates = pushResults.map((x) =>
        getTaskState(
          x.state,
          auditResults.find((audit) => audit.taskId === x.taskId)?.auditState ?? null,
        ),
      )
      const oldState = taskSet.state
      const oldHasFailedTask = taskSet.hasFailedTask
      taskSet.updateState(taskStates)
      await pushingTaskSetStore.save(taskSet)
      if (oldState !== taskSet.state || oldHasFailedTask !== taskSet.hasFailedTask) {
        eventBus.emit(pushEvents.pushingSetStateChanged, {
          taskSetId: taskSet.taskSetId,
          state: taskSet.state,
          hasFailedTask: taskSet.hasFailedTask,
        })
      }
    },
    [platformAuditResultStore, publishStore, pushingTaskPushResultStore, pushingTaskSetStore],
  )

  const getCloudPushingTaskViewModel = useCallback(
    (taskSetId: PushingTaskSetIdentifier) => pushingTaskApi.getTasksByTaskSetId(taskSetId),
    [pushingTaskApi],
  )

  const getConfigs = useCallback(
    async (taskIds: string[]) => {
      const tasks = await pushingTaskStore.getMany(taskIds)
      return await pushingConfigStore.getConfigs(tasks.map((x) => x.pushingConfigId!))
    },
    [pushingTaskStore, pushingConfigStore],
  )

  return useMemo(
    () => ({
      startSuperLock,
      startSuperOccupy,
      publishCloudForArticle,
      publishCloudForImageText,
      startLocalTask,
      resumeUnfinishedPushingTasks,
      reportBrowserTask,
      resumeSchedulingPlatformStateQuery,
      scheduleAuditStateQuery,
      auditStateQuery,
      succeedQueryAuditResult,
      prepareForPush,
      startPush,
      getCloudPushingTaskSetsViewModel,
      reportTasks,
      updateCloudPushResult,
      generatePushingConfigsForSingleVideo,
      generatePushingConfigsForMultipleVideo,
      generatePushingConfigsForImageText,
      generatePushingConfigsForArticle,
      rePushTaskSet,
      updatePushingTaskSetState,
      getCloudPushingTaskViewModel,
      updateProgress,
      succeedTask,
      failTask,
      cancelCloudTask,
      cancelLocalTask,
      getUnfinishedTaskIds,
      deleteTask,
      deletePushingTask,
      getConfigs,
      publishCloudForSingleVideo,
      publishCloudForMultipleVideo,
    }),
    [
      startSuperLock,
      startSuperOccupy,
      publishCloudForArticle,
      publishCloudForImageText,
      startLocalTask,
      resumeUnfinishedPushingTasks,
      reportBrowserTask,
      resumeSchedulingPlatformStateQuery,
      scheduleAuditStateQuery,
      auditStateQuery,
      succeedQueryAuditResult,
      prepareForPush,
      startPush,
      getCloudPushingTaskSetsViewModel,
      reportTasks,
      updateCloudPushResult,
      generatePushingConfigsForSingleVideo,
      generatePushingConfigsForMultipleVideo,
      generatePushingConfigsForImageText,
      generatePushingConfigsForArticle,
      rePushTaskSet,
      updatePushingTaskSetState,
      getCloudPushingTaskViewModel,
      updateProgress,
      succeedTask,
      failTask,
      cancelCloudTask,
      cancelLocalTask,
      getUnfinishedTaskIds,
      deleteTask,
      deletePushingTask,
      getConfigs,
      publishCloudForSingleVideo,
      publishCloudForMultipleVideo,
    ],
  )

  function convertArticleFormData(
    contentConfig: ArticlePublishViewModel,
    toDraft: boolean,
  ): ArticleTaskSetForm {
    return {
      topic: contentConfig.topic,
      locationKeyword: contentConfig.locationKeyword,
      accounts: [],
      cover: undefined,
      verticalCover: undefined,
      categories: {
        [platformNames.BaiJiaHao]: contentConfig.category.map((x) => ({
          id: x,
          text: x,
          raw: x,
        })),
      },
      timing: contentConfig.timing,
      isFirst: contentConfig.isFirst,
      title: contentConfig.title,
      content: contentConfig.content,
      isDraft: toDraft,
    }
  }

  function convertImageTextFormData(
    contentConfig: ImageTextPublishViewModel,
    toDraft: boolean,
  ): ImageTextTaskSetForm {
    return {
      accounts: [],
      title: contentConfig.title,
      description: contentConfig.description,
      timing: contentConfig.timing,
      location: Object.fromEntries(
        Object.entries(contentConfig.location).map(([platform, value]) => [
          platform,
          value === null ? undefined : value,
        ]),
      ),

      images: [],
      cover: undefined,
      music: Object.fromEntries(
        Object.entries(contentConfig.music).map(([platform, value]) => [
          platform,
          value === null ? undefined : value,
        ]),
      ),

      isDraft: toDraft,
    } satisfies ImageTextTaskSetForm
  }

  function convertVideoFormData(
    contentConfig: VideoContentViewModel,
    toDraft: boolean,
  ): VideoTaskSetForm {
    return {
      accounts: [],
      aPlatform: {
        title: contentConfig.aPlatformForm.title,
        description: contentConfig.aPlatformForm.description,
      },
      bPlatform: {
        title: contentConfig.bPlatformForm.title,
        description: contentConfig.bPlatformForm.description,
        tags: contentConfig.bPlatformForm.tags,
      },
      isOriginal: contentConfig.isOriginal,
      timing: contentConfig.timing,
      location: Object.fromEntries(
        Object.entries(contentConfig.location).map(([platform, value]) => [
          platform,
          value === null ? undefined : value,
        ]),
      ),
      categories: Object.fromEntries(
        Object.entries(contentConfig.categories).map(([platform, value]) => [
          platform,
          value === null ? undefined : value,
        ]),
      ),

      isDraft: toDraft,
    } satisfies VideoTaskSetForm
  }
}
