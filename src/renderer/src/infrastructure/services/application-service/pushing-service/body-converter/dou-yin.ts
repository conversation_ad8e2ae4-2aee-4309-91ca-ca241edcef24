import type {
  Account,
  ImageFileInfo,
  VideoContentViewModel,
  VideoFileInfo,
} from '@renderer/infrastructure/model'
import { platforms } from '@renderer/infrastructure/model'

import type { PushingTaskBodyConverter } from './pushing-task-body-converter'
import { ignoreException } from './pushing-task-body-converter'
import { utils } from '@coozf/editor'
import type {
  AccountSession,
  MusicPlatformDataItem,
  PlatformDataItem,
  WithLocalStorage,
} from '@common/structure'
import type { DouyinLocationInfo } from '@yixiaoer/platform-service/dist/media-platform/douyin/douyin-location.service'
import { datetimeService, douYinPlatformService } from '@renderer/infrastructure/services'
import type { LiteralObject } from '@common/infrastructure/type/literal-object'
import type { DouyinVideoTaskInput } from '@yixiaoer/platform-service/dist/media-platform/douyin/douyin-video'
import type {
  LocalImageTextPushingConfig,
  LocalVideoPushingConfig,
} from '@renderer/infrastructure/model/config/pushing-config'
import type { DouyinDynamicTaskInput } from '@yixiaoer/platform-service/dist/media-platform/douyin/douyin-article'
import type { DouyinMusicInfo } from '@yixiaoer/platform-service/dist/media-platform/douyin/douyin'
import type { PushContentType } from '@common/model/content-type'

class DouYinPushingTaskBodyConverter implements PushingTaskBodyConverter {
  async toVideo(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    pushingConfig: LocalVideoPushingConfig,
    video: VideoFileInfo,
    cover: ImageFileInfo,
    title: string,
    description: string,
    isOriginal: boolean,
    locationKeyword: string,
    toDraft: boolean,
    timing?: number,
  ): Promise<LiteralObject> {
    // 转换话题
    description = await this.getDescription(description, session)
    // 获取地理位置
    const location = await this.getLocation(
      locationKeyword,
      pushingConfig.location,
      account,
      session,
    )
    return {
      localStorage: session.localStorage,
      taskId: taskId,
      video: {
        duration: video.fileDurationTimeSpan.seconds,
        width: video.videoWidth,
        height: video.videoHeight,
        size: video.fileSize,
        localPath: video.filePath,
      },
      cover: {
        width: cover.width,
        height: cover.height,
        size: cover.size,
        pathOrUrl: cover.path,
      },
      challenges: [],
      mentions: [],
      title: title,
      text: description,
      visibility_type: toDraft ? 1 : 0,
      download: 0,
      tag_level1_id: undefined,
      tag_level2_id: undefined,
      tag_level3_id: undefined,
      tag_level3_name: undefined,
      mix_id: undefined,
      mix_order: undefined,
      timing: datetimeService.toSeconds(timing),
      poi_id: location?.raw.poi_id,
      poi_name: location?.raw.poi_name,
      hot_sentence: undefined,
      miniAppInfo: undefined,

      goodsInfoList: undefined,
      should_sync: 0,
      activity: undefined,
      declareInfo: undefined,
    } satisfies DouyinVideoTaskInput & WithLocalStorage
  }

  async toDynamic(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    config: LocalImageTextPushingConfig,
    toDraft: boolean,
  ): Promise<LiteralObject> {
    // 转换话题
    const description = await this.getDescription(config.description, session)
    // 获取地理位置
    const location = await this.getLocation(
      config.locationKeyword,
      config.location,
      account,
      session,
    )
    const music = config.music?.[platforms.DouYin.name] as MusicPlatformDataItem<DouyinMusicInfo>
    return {
      localStorage: session.localStorage,
      taskId: taskId,
      cover: {
        width: config.cover.width,
        height: config.cover.height,
        size: config.cover.size,
        pathOrUrl: config.cover.path,
      },
      images: config.images.map((image) => ({
        width: image.width,
        height: image.height,
        size: image.size,
        pathOrUrl: image.path,
      })),
      challenges: [],
      mentions: [],
      title: config.title,
      text: description,
      visibility_type: toDraft ? 1 : 0,
      download: 0,
      poi_id: location?.raw.poi_id,
      poi_name: location?.raw.poi_name,
      musicInfo: music?.raw ?? undefined,
      activity: [],
      timing: datetimeService.toSeconds(config.timing),
    } satisfies DouyinDynamicTaskInput & WithLocalStorage
  }

  private async getLocation(
    locationKeyword: string,
    configLocation: VideoContentViewModel['location'] | null,
    account: Account,
    session: AccountSession,
  ) {
    if (configLocation && configLocation[platforms.DouYin.name]) {
      return configLocation[platforms.DouYin.name] as PlatformDataItem<DouyinLocationInfo>
    }
    let location: PlatformDataItem<DouyinLocationInfo> | undefined
    await ignoreException(async () => {
      location = locationKeyword
        ? // locationType 0 为普通模式
          (await douYinPlatformService.getLocations(account, session, locationKeyword, 0))[0] ??
          undefined
        : undefined
    })
    return location
  }

  private async getDescription(description: string, session: AccountSession): Promise<string> {
    return await utils.commonTopic.modifyTopics(description, async (element) => {
      await ignoreException(async () => {
        const keyword = element.getAttribute('text')
        if (!keyword) return

        const topic = (await douYinPlatformService.getTopics(session, keyword))[0] ?? undefined
        if (!topic) element.remove()
        else element.setAttribute('raw', JSON.stringify(topic.raw))
      })
    })
  }
}

export const douYin = new DouYinPushingTaskBodyConverter()
