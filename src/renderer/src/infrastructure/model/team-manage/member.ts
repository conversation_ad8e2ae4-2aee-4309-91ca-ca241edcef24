export class Member {
  constructor(
    public id: string,
    public phone: string,
    public nickName: string,
    public remark: string,
    public avatarUrl: string,
    public status: string,
    public roles: string[],
    public createdAt: string,
    public accountCount: number,
    public webSpaceCount: number,
    public isFreeze: boolean,
    public memberId: string,
    public maxAccountCount: number,
  ) {}

  get displayName() {
    return this.remark || this.nickName
  }
}
