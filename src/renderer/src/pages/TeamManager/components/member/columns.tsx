import type { ColumnDef } from '@tanstack/react-table'
import type { Member } from '@renderer/infrastructure/model/team-manage/member'
import { RoleType } from '@renderer/infrastructure/model/team-manage/role-type'
import { JoinStatus } from '@renderer/infrastructure/model/team-manage/join-status'
import EditIcon from '@renderer/assets/team/edit.svg?react'

import { datetimeService } from '@renderer/infrastructure/services'
import { ManagerRestricted } from '@renderer/components/ManagerRestricted'
import { MasterRestricted } from '@renderer/components/MasterRestricted'
import { cn } from '@renderer/lib/utils'
import DropdownMenuComponent from '@renderer/components/DropdownMenuComponent'
import { DropdownMenuItem } from '@renderer/shadcn-components/dropdown-menu'
import type { UserInfo } from '@renderer/infrastructure/model'
import OverflowTooltip from '@renderer/components/OverflowTooltip'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  Sheet<PERSON>itle,
  SheetTrigger,
} from '@renderer/shadcn-components/ui/sheet'
import { VisuallyHidden } from '@radix-ui/react-visually-hidden'
import { SheetOperator } from '@renderer/components/Operator/SheetOperator'

function getFreezeClass(isFreeze?: boolean) {
  return isFreeze ? 'grayscale opacity-50' : ''
}

export function getTableColumns(
  userInfo: UserInfo | null,
  openSetAccountOperator: (id: string) => void,
  handleSetAdmin: (id: string, role: string) => void,
  handleRemoveMember: (id: string) => void,
  unFreezeMember: (id: string) => void,
  remarkMember: (member: Member) => void,
): Array<ColumnDef<Member>> {
  // 恢复团队成员
  return [
    {
      header: '成员信息',
      accessorKey: 'memberInfo',
      cell: ({ row }) => {
        const { displayName, avatarUrl, roles, isFreeze } = row.original
        const freezeClass = getFreezeClass(isFreeze)

        return (
          <div className="flex w-48 items-center gap-2">
            <div className={cn('shrink-0 overflow-hidden rounded-full', freezeClass)}>
              <img src={avatarUrl} className={'h-8 w-8 scale-[1.1]'} alt={'xxx'} />
            </div>

            <div className="flex flex-grow-0 flex-col justify-center overflow-hidden">
              <OverflowTooltip tooltip={displayName} className={cn('flex-1', freezeClass)}>
                {displayName}
              </OverflowTooltip>
              {isFreeze && (
                <div className="flex items-center text-xs text-destructive">
                  <div className="mx-1 inline-block h-1.5 w-1.5 rounded-sm bg-destructive" />
                  不可用
                </div>
              )}
            </div>
            <div className={`shrink-0 ${freezeClass}`}>
              <ManagerRestricted>
                <EditIcon
                  className="hidden h-5 w-5 shrink-0 cursor-pointer group-hover:block"
                  onClick={() => remarkMember(row.original)}
                ></EditIcon>
              </ManagerRestricted>
              <div className="flex flex-shrink-0 items-center gap-1 text-xs leading-4 group-hover:hidden">
                {roles.includes(RoleType.Master) && (
                  <span className="rounded bg-[#E5E3FB] px-1 py-0.5 font-medium text-[#4E3EE9]">
                    创建人
                  </span>
                )}
                {userInfo?.id === row.original.id && (
                  <span className="rounded bg-[#D0DBFA] px-1 py-0.5 font-medium text-[#1456F0]">
                    我
                  </span>
                )}
              </div>
            </div>
          </div>
        )
      },
    },
    {
      header: '手机号',
      accessorKey: 'phone',
      cell: ({ row }) => {
        const { phone, isFreeze } = row.original
        const freezeClass = getFreezeClass(isFreeze)
        return <div className={`${freezeClass}`}>{phone}</div>
      },
    },
    {
      header: '运营账号',
      accessorKey: 'accountCount',
      cell: ({ row }) => {
        const { id, accountCount, status, isFreeze, memberId, maxAccountCount } = row.original
        const freezeClass = getFreezeClass(isFreeze)
        return (
          <div className={cn('flex w-16', freezeClass)}>
            <span>{accountCount}</span>

            {status === JoinStatus.Joined && (
              <ManagerRestricted>
                <Sheet>
                  <SheetTrigger asChild>
                    <span className="ml-2 hidden cursor-pointer text-sm font-normal text-[#4F46E5] group-hover:inline-flex">
                      设置
                    </span>
                  </SheetTrigger>
                  <SheetContent className="flex flex-col gap-0 p-0">
                    <SheetHeader className="border-b px-6 py-4">
                      <SheetTitle>设置账号</SheetTitle>
                      <VisuallyHidden>
                        <SheetDescription>请选择账号</SheetDescription>
                      </VisuallyHidden>
                    </SheetHeader>
                    <SheetOperator
                      accountCount={accountCount}
                      memberId={memberId}
                      userId={id}
                      maxAccountCount={maxAccountCount}
                    />
                  </SheetContent>
                </Sheet>
              </ManagerRestricted>
            )}
          </div>
        )
      },
    },
    {
      header: '加入时间',
      accessorKey: 'createdAt',
      cell: ({ row }) => {
        const { createdAt, isFreeze } = row.original
        return (
          <div className={cn('min-w-20', isFreeze ? 'opacity-50 grayscale' : '')}>
            <span>{datetimeService.formatToDate(createdAt)}</span>
          </div>
        )
      },
    },
    {
      header: '角色',
      accessorKey: 'roles',
      cell: ({ row }) => {
        const { roles, status, isFreeze } = row.original
        const role = roles[0]
        const roleText =
          status === JoinStatus.Pending
            ? '待确认'
            : role === RoleType.Master
              ? '创建人'
              : role === RoleType.Admin
                ? '管理员'
                : '成员'
        return (
          <div
            className={
              status === JoinStatus.Pending
                ? cn('w-12 text-[#4F46E5]', isFreeze ? 'opacity-50 grayscale' : '')
                : cn('w-12 text-[#222222]', isFreeze ? 'opacity-50 grayscale' : '')
            }
          >
            {roleText}
          </div>
        )
      },
    },
    {
      header: '操作',
      accessorKey: 'action',
      cell: ({ row }) => {
        const member = row.original
        const currentRole = member.roles[0]
        const status = member.status
        // 是否有权限
        const hasPermission = !(userInfo?.id === member.id || currentRole === RoleType.Master)
        if (!hasPermission) {
          return null
        }
        return (
          <ManagerRestricted>
            <DropdownMenuComponent>
              {status === JoinStatus.Joined && (
                <MasterRestricted>
                  <DropdownMenuItem onClick={() => handleSetAdmin(member.id, currentRole)}>
                    {currentRole === RoleType.Admin ? '取消管理员' : '设为管理员'}
                  </DropdownMenuItem>
                </MasterRestricted>
              )}
              <DropdownMenuItem variant="destructive" onClick={() => handleRemoveMember(member.id)}>
                移除成员
              </DropdownMenuItem>
              {row.original.isFreeze && (
                <DropdownMenuItem onClick={() => unFreezeMember(row.original.id)}>
                  恢复
                </DropdownMenuItem>
              )}
            </DropdownMenuComponent>
          </ManagerRestricted>
        )
      },
    },
  ]
}
