import { FormItem, FormItem as PublishFormItem } from './FormItem'
import { AccountsSelector } from '@renderer/pages/Publish/components/AccountsSelector'
import type { SpiderAccount } from '@renderer/infrastructure/model'
import {
  type Account,
  type ImageFileInfo,
  type ImageTextPublishViewModel,
} from '@renderer/infrastructure/model'
import { produce } from 'immer'
import { ImageSelector } from '@renderer/pages/Publish/components/ImageSelector'
import { ValidationMessage } from '@renderer/components/ValidationMessage'
import { useValidation } from '@renderer/hooks/validation/validation'
import { RuleResult } from '@renderer/infrastructure/validation/rule'
import { Validator } from '@renderer/infrastructure/validation/validator'
import CoverSelector from './cover-selector'
import { MusicSelector } from '@renderer/pages/Publish/components/musicSelector'
import { DescriptionEditor } from '@renderer/pages/Publish/components/DescriptionEditor'
import type { Updater } from 'use-immer'
import { useMemo } from 'react'

import { useImageTextVisualHint } from '@renderer/pages/Publish/visual-hint/image-text'
import {
  useCoverValidator,
  useDescriptionValidator,
  useImagesValidator,
  useTitleValidator,
  useTopicValidator,
} from '../validation/useImageTextContentValidation'
import { htmlService } from '@renderer/infrastructure/services'
import { HelpTooltop } from '@renderer/components/helpTooltop'
import { DateTimePicker } from '@renderer/components/DateTimePicker'
import { useScheduledTimeValidator } from '@renderer/pages/Publish/validation/useImageTextContentValidation'
import { getImageTextSpecification } from '@renderer/pages/Publish/specification/content-type/image-text/image-text-specification'
import { useSummaryContext } from '@renderer/infrastructure/validation/use-summary-context'
import {
  CommonSummaryContext,
  FormStateContext,
  PlatformSummaryContext,
} from '@renderer/pages/Publish/context/summary-context'
import { useFormStateContext } from '@renderer/infrastructure/validation/use-form-state-context'
import { sort } from '@renderer/utils/array'
import { imageTextPlatforms } from '@renderer/pages/Publish/specification/content-type/supports'
import { PositionSelector } from './positionSelect.tsx'
import { PublishInput } from '@renderer/components/PublishInput'
import { SessionCheckProgress, SessionValidSummary } from './session-check/session-check-progress'
import { useScenarioContext } from '@renderer/context/scenario-context'
import { useWechat3rdPartyService } from '@renderer/infrastructure/services/application-service/account/wechat-3rd-party-service'
import { PlatformRepresentativeAccountContextProvider } from '@renderer/context/platform-representative-account-context'

const validators = {
  accounts: new Validator<Account[]>().addRule((subject) => {
    if (subject.length === 0) {
      return new RuleResult('invalid', '请添加至少一个账号')
    }
    return RuleResult.valid
  }),
  accountsValid: new Validator<Account[]>().addRule((subject) => {
    if (subject.some((account) => !account.isSessionValid())) {
      return new RuleResult('invalid', '失效账号无法发布')
    }
    return RuleResult.valid
  }),
}

export function ImageTextForm({
  config,
  setConfig,
}: {
  config: ImageTextPublishViewModel
  setConfig: Updater<ImageTextPublishViewModel>
}) {
  const formState = useFormStateContext(FormStateContext)

  const commonSummary = useSummaryContext(CommonSummaryContext)
  const platformSummary = useSummaryContext(PlatformSummaryContext)

  const selectedPlatforms = useMemo(
    () =>
      Array.from(new Set(config.accounts.map((x) => x.platform))).sort(
        sort.by((x) => x.displayOrder),
      ),
    [config.accounts],
  )
  const { conclusion: accountsConclusion } = useValidation(
    config.accounts,
    validators.accounts,
    commonSummary,
  )
  useValidation(config.accounts, validators.accountsValid, commonSummary)

  useValidation(config.images, useImagesValidator(selectedPlatforms), platformSummary)

  useValidation(config.cover, useCoverValidator(selectedPlatforms), platformSummary)

  useValidation(
    useMemo(() => htmlService.getDescriptionPureText(config.description), [config.description]),
    useDescriptionValidator(selectedPlatforms),
    platformSummary,
  )
  useValidation(
    useMemo(() => htmlService.getTopics(config.description), [config.description]),
    useTopicValidator(selectedPlatforms),
    platformSummary,
  )

  useValidation(config.title, useTitleValidator(selectedPlatforms), platformSummary)

  useValidation(config.timing, useScheduledTimeValidator(selectedPlatforms), platformSummary)

  const visualHint = useImageTextVisualHint(selectedPlatforms)

  // region 微信场景/锁定相关

  const { setAccounts, accountsWithTokens } = useScenarioContext()
  const { updateAccountLocks } = useWechat3rdPartyService()

  // endregion

  return (
    <PlatformRepresentativeAccountContextProvider accounts={config.accounts}>
      <div className="m-3 h-0 flex-grow overflow-y-scroll rounded-lg bg-white shadow-sm">
        <div className="flex w-full p-4 pr-2.5">
          <div className="w-0 flex-grow rounded-lg pr-0">
            <div className="m-4 flex flex-col gap-8">
              <PublishFormItem label="账号" required={true} contentWidthLimited={false}>
                <div>
                  <AccountsSelector
                    selectedAccounts={config.accounts}
                    onChange={async (accounts: Account[]) => {
                      const newAccountsWithTokens = await updateAccountLocks(
                        accounts as SpiderAccount[],
                        accountsWithTokens,
                      )
                      setAccounts(newAccountsWithTokens)
                      formState.setDirty(true)
                      setConfig(
                        produce(config, (draft) => {
                          draft.accounts = accounts as SpiderAccount[] //这里目前选出来的必然是SpiderAccount
                        }),
                      )
                    }}
                    selectable={(account: Account) =>
                      getImageTextSpecification(account.platform).contentTypeSupport
                    }
                    platforms={imageTextPlatforms}
                  />
                  <ValidationMessage
                    subject={config.accounts}
                    conclusion={accountsConclusion}
                    formState={formState}
                  />
                </div>

                <SessionCheckProgress />
                <SessionValidSummary accounts={config.accounts} />
              </PublishFormItem>

              <PublishFormItem
                label="图片"
                required={visualHint.imageRequired}
                contentWidthLimited={false}
              >
                <ImageSelector
                  images={config.images}
                  onChange={(images: ImageFileInfo[]) => {
                    formState.setDirty(true)
                    setConfig(
                      produce(config, (draft) => {
                        draft.images = images
                        if (images.length > 0 && !draft.cover) {
                          draft.cover = images[0]
                        }
                      }),
                    )
                  }}
                  setCover={(image: ImageFileInfo) => {
                    setConfig(
                      produce(config, (draft) => {
                        draft.cover = image
                      }),
                    )
                  }}
                  selectedPlatforms={selectedPlatforms}
                  showSetCoverButton={visualHint.coverSupport}
                />
              </PublishFormItem>
              {visualHint.coverSupport && (
                <PublishFormItem label="封面" required={visualHint.coverRequired}>
                  <CoverSelector
                    video={null}
                    cover={config.cover}
                    onChange={(cover: ImageFileInfo | null) => {
                      formState.setDirty(true)
                      setConfig(
                        produce(config, (draft) => {
                          draft.cover = cover
                        }),
                      )
                    }}
                  />
                </PublishFormItem>
              )}
              {visualHint.titleSupport && (
                <PublishFormItem label="标题" required={visualHint.titleRequired}>
                  <PublishInput
                    type="text"
                    value={config.title}
                    onChange={(e) => {
                      formState.setDirty(true)
                      setConfig((x) => {
                        x.title = e.target.value
                      })
                    }}
                  />
                </PublishFormItem>
              )}
              <div className="flex flex-col space-y-4 pr-4">
                <div className="flex flex-col space-y-9">
                  <PublishFormItem label="描述" required={visualHint.descriptionRequired}>
                    <DescriptionEditor
                      description={config.description}
                      onChange={(description) => {
                        formState.setDirty(true)
                        setConfig((x) => {
                          x.description = description
                        })
                      }}
                    />
                  </PublishFormItem>
                  {visualHint.musicSupport && (
                    <PublishFormItem label="音乐">
                      <MusicSelector
                        platforms={visualHint.musicPlatforms}
                        music={config.music}
                        onChange={(music: typeof ImageTextPublishViewModel.prototype.music) => {
                          formState.setDirty(true)
                          setConfig(
                            produce(config, (draft) => {
                              draft.music = music
                            }),
                          )
                        }}
                      />
                    </PublishFormItem>
                  )}
                  {visualHint.locationSupport && (
                    <PublishFormItem contentWidthLimited={false} label="位置">
                      <PositionSelector
                        selectedAccounts={config.accounts}
                        value={config.location}
                        onChange={(location) => {
                          formState.setDirty(true)
                          setConfig((x) => {
                            x.location = location
                          })
                        }}
                        allowedPlatforms={visualHint.locationPlatforms}
                      />
                    </PublishFormItem>
                  )}
                  {visualHint.scheduledTimeSupport && (
                    <FormItem
                      label={
                        <div className="flex items-center gap-1">
                          <span>定时发送</span>
                          <HelpTooltop title="设置后作品将推送至平台以设定的时间发布" />
                        </div>
                      }
                    >
                      <DateTimePicker
                        timestamp={config.timing}
                        onChange={(timing) => {
                          formState.setDirty(true)
                          setConfig((x) => {
                            x.timing = timing
                          })
                        }}
                      />
                    </FormItem>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PlatformRepresentativeAccountContextProvider>
  )
}
